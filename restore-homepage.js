// Script to restore a complete homepage configuration
import fetch from 'node-fetch';

const baseUrl = 'http://localhost:3002';

// Login credentials
const credentials = {
  username: 'admin',
  password: 'admin123',
  rememberMe: false
};

// Complete homepage configuration with multiple sections
const completeHomepageConfig = {
  sections: [
    // Hero Section (existing)
    {
      id: 'hero-1',
      type: 'hero',
      title: 'Hero Section',
      enabled: true,
      order: 1,
      content: {
        title: 'Premium Digital Invoice Solutions',
        subtitle: 'Professional & Modern',
        description: 'Create, manage, and send professional invoices with our comprehensive digital invoice platform. Streamline your billing process and get paid faster.',
        ctaText: 'Get Started',
        ctaLink: '#products',
        backgroundImage: '',
        backgroundType: 'gradient',
        backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        textColor: '#ffffff',
        showVideo: false,
        videoUrl: ''
      }
    },
    // Features Section
    {
      id: 'features-1',
      type: 'features',
      title: 'Features Section',
      enabled: true,
      order: 2,
      content: {
        title: 'Powerful Features',
        subtitle: 'Everything you need to manage your invoices professionally',
        features: [
          {
            id: 'feature-1',
            icon: '📄',
            title: 'Professional Templates',
            description: 'Choose from beautiful, customizable invoice templates that reflect your brand.',
            enabled: true
          },
          {
            id: 'feature-2',
            icon: '⚡',
            title: 'Fast Processing',
            description: 'Generate and send invoices in seconds with our streamlined workflow.',
            enabled: true
          },
          {
            id: 'feature-3',
            icon: '💳',
            title: 'Multiple Payment Options',
            description: 'Accept payments via PayPal, credit cards, and other popular methods.',
            enabled: true
          },
          {
            id: 'feature-4',
            icon: '📊',
            title: 'Analytics & Reporting',
            description: 'Track your income, expenses, and payment status with detailed reports.',
            enabled: true
          },
          {
            id: 'feature-5',
            icon: '🔒',
            title: 'Secure & Reliable',
            description: 'Your data is protected with enterprise-grade security measures.',
            enabled: true
          },
          {
            id: 'feature-6',
            icon: '📱',
            title: 'Mobile Friendly',
            description: 'Access and manage your invoices from any device, anywhere.',
            enabled: true
          }
        ],
        layout: 'grid',
        columns: 3
      }
    },
    // Products Section
    {
      id: 'products-1',
      type: 'products',
      title: 'Products Section',
      enabled: true,
      order: 3,
      content: {
        title: 'Our Services',
        subtitle: 'Choose the perfect plan for your business needs',
        showAllProducts: true,
        featuredProductIds: [],
        layout: 'grid',
        columns: 3,
        showPrices: true,
        showDescriptions: true
      }
    },
    // CTA Section
    {
      id: 'cta-1',
      type: 'cta',
      title: 'Call to Action',
      enabled: true,
      order: 4,
      content: {
        title: 'Ready to Streamline Your Invoicing?',
        description: 'Join thousands of businesses who trust our platform for their invoicing needs. Start creating professional invoices today.',
        primaryButtonText: 'Start Free Trial',
        primaryButtonLink: '#products',
        secondaryButtonText: 'View Demo',
        secondaryButtonLink: '#features',
        backgroundType: 'gradient',
        backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        backgroundImage: '',
        textColor: '#ffffff'
      }
    }
  ],
  seo: {
    title: 'Digital Invoice Pro - Professional Invoicing Solutions',
    description: 'Create, manage, and send professional invoices with our comprehensive digital invoice platform. Streamline your billing process and get paid faster.',
    keywords: 'digital invoice, invoicing software, billing, payment processing, business tools',
    ogTitle: 'Digital Invoice Pro - Professional Invoicing Solutions',
    ogDescription: 'Create, manage, and send professional invoices with our comprehensive digital invoice platform.',
    ogImage: '',
    twitterTitle: 'Digital Invoice Pro',
    twitterDescription: 'Professional invoicing solutions for modern businesses.',
    twitterImage: ''
  },
  theme: {
    primaryColor: '#667eea',
    secondaryColor: '#764ba2',
    accentColor: '#8b5cf6',
    backgroundColor: '#ffffff',
    textColor: '#1e293b',
    fontFamily: 'Inter, system-ui, sans-serif',
    borderRadius: '8px',
    spacing: '1rem'
  }
};

async function restoreHomepage() {
  try {
    console.log('🔐 Logging in to admin...');

    // Login to admin
    const loginResponse = await fetch(`${baseUrl}/api/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
      credentials: 'include'
    });

    if (!loginResponse.ok) {
      const errorText = await loginResponse.text();
      throw new Error(`Login failed: ${loginResponse.statusText} - ${errorText}`);
    }

    // Get session cookie
    const setCookieHeader = loginResponse.headers.raw()['set-cookie'];
    const sessionCookie = setCookieHeader ? setCookieHeader.find(cookie => cookie.includes('connect.sid')) : null;
    console.log('✅ Login successful');

    console.log('🏠 Updating homepage configuration...');

    // Update homepage configuration
    const updateResponse = await fetch(`${baseUrl}/api/homepage`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': sessionCookie || ''
      },
      body: JSON.stringify(completeHomepageConfig),
      credentials: 'include'
    });

    if (!updateResponse.ok) {
      const errorText = await updateResponse.text();
      throw new Error(`Homepage update failed: ${updateResponse.statusText} - ${errorText}`);
    }

    const result = await updateResponse.json();
    console.log('✅ Homepage configuration updated successfully!');
    console.log(`📊 Sections added: ${result.sections.length}`);
    console.log('🎉 Your homepage now includes:');
    result.sections.forEach((section, index) => {
      console.log(`   ${index + 1}. ${section.title} (${section.type})`);
    });

  } catch (error) {
    console.error('❌ Error restoring homepage:', error.message);
  }
}

// Run the restoration
restoreHomepage();
